{"name": "dexta-app", "version": "0.1.0", "homepage": "/", "private": true, "scripts": {"start": "PORT=3006 react-scripts start", "build": "CI=false react-scripts --max_old_space_size=5120 build", "test": "react-scripts test", "eject": "react-scripts eject", "dev": "set PORT=3006 && react-scripts start"}, "dependencies": {"@emotion/react": "11.10.4", "@emotion/styled": "11.10.4", "@headlessui/react": "^1.7.17", "@leecheuk/react-google-login": "^5.4.1", "@mui/icons-material": "^5.14.14", "@mui/material": "5.10.5", "@reduxjs/toolkit": "^1.9.5", "@stripe/react-stripe-js": "^2.4.0", "@stripe/stripe-js": "^2.1.6", "@tanstack/react-query": "^4.33.0", "@tanstack/react-query-devtools": "^4.33.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@uiw/react-color": "^1.4.2", "@univerjs/presets": "0.6.6", "add": "^2.0.6", "axios": "^1.4.0", "buffer": "^6.0.3", "chart.js": "^4.4.2", "chartjs-plugin-annotation": "^3.0.1", "country-flag-icons": "^1.5.18", "formik": "^2.4.3", "geoip-country": "^5.0.202505202342", "intro.js": "^7.2.0", "intro.js-react": "^1.0.0", "ip-address": "^10.0.1", "ipapi.co": "^0.3.0", "jwt-decode": "^4.0.0", "moment": "^2.29.4", "prop-types": "^15.8.1", "query-string": "^8.1.0", "react": "^18.2.0", "react-avatar-editor": "^13.0.0", "react-chartjs-2": "^5.2.0", "react-clock": "^4.5.1", "react-custom-scrollbars": "^4.2.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-easy-crop": "^5.0.2", "react-google-recaptcha": "^3.1.0", "react-html-parser": "^2.0.2", "react-icons": "^4.11.0", "react-input-range": "^1.3.0", "react-loader-spinner": "4.0.0", "react-loading-skeleton": "^3.3.1", "react-multi-select-component": "^4.3.4", "react-number-to-words": "^1.0.5", "react-phone-number-input": "^3.3.6", "react-quill": "^2.0.0", "react-redux": "^8.1.2", "react-router-dom": "6.11.2", "react-scripts": "5.0.1", "react-toastify": "^9.1.3", "react-transition-group": "^4.4.5", "react-webcam": "^7.1.1", "redux": "^4.2.1", "redux-persist": "^6.0.0", "rxjs": "^7.8.2", "stream": "^0.0.2", "video.js": "^8.21.0", "web-vitals": "^2.1.4", "worker-loader": "^3.0.8", "workerize-loader": "^2.0.2", "xlsx": "^0.18.5", "yarn": "^1.22.21", "yup": "^1.2.0"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"tailwindcss": "^3.3.3"}}