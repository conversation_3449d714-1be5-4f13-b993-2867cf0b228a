import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useState, useRef, useEffect } from "react";
import { GoArrowRight } from "react-icons/go";
import { BiTime } from "react-icons/bi";
import { getChat } from "./hooks/getChat";
import queryString from "query-string";
import { useLocation } from "react-router-dom";
import { createChat } from "./hooks/createChat";
import { getModuleByID } from "../Profile/MyAssessments/Assessments-main/hooks/getModuleByID";
import d1 from "../../Dexta_assets/d1.png";
import d2 from "../../Dexta_assets/d2.png";
import d3 from "../../Dexta_assets/d3.png";
import d4 from "../../Dexta_assets/d4.png";
import closem from "../../Dexta_assets/closeModal.png";
import ReactHtmlParser from "react-html-parser";
import styles from "../Profile/MyAssessments/Assessments-main/styling2.module.css";

const Chat = () => {
  const [inputMessage, setInputMessage] = useState("");
  const messagesEndRef = useRef(null);
  const location = useLocation();
  const parsed = queryString.parse(location.search);
  const queryClient = useQueryClient();
  const [ModuleId, setModuleID] = useState(0);
  const [ModuleDetailsModal, setModuleDetailsModal] = useState(false);
  //#region Fetching user chat
  const { data: chatData, isLoading: chatLoading } = useQuery(
    ["chat", parsed?.chat_id],
    () => getChat(parsed?.chat_id),
    {
      enabled: !!parsed?.chat_id,
    }
  );
  //#endregion

  //#region Sending message
  const { mutate: messageMutate, isLoading: messageLoading } = useMutation(
    createChat,
    {
      onSuccess: () => {
        queryClient.invalidateQueries("/langchain/chats");
        setInputMessage("");
      },
    }
  );
  //#endregion

  //#region Scroll to bottom of chat when messages change
  useEffect(() => {
    if (chatData || messageLoading) {
      messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    }
  }, [chatData, messageLoading]);
  //#endregion

  //#region Handle send message
  const handleSendMessage = (e) => {
    e.preventDefault();
    if (inputMessage.trim() === "") return;
    let data = JSON.stringify({
      query: inputMessage,
      chatId: parseInt(parsed?.chat_id),
    });
    try {
      messageMutate(data);
    } catch (err) {
      console.log(err.message);
    }
  };
  //#endregion

  //#region Fetching modules Data
  const {
    data: ModuleData,
    error: errorModule,
    isLoading: isLoadingModule,
  } = useQuery(["sections", ModuleId], () => getModuleByID(ModuleId));
  //#endregion

  function handleButtonClick(event) {
    event.stopPropagation();
  }

  if (chatLoading) {
    return (
      <div class="loader-container-1 col-span-6">
        <div class="loader-1"></div>
      </div>
    );
  }

  return (
    <>
      <div className="flex flex-col w-full h-full">
        {/* Chat messages area */}
        <div className="flex-1 overflow-y-auto p-4">
          <div className="max-w-4xl mx-auto">
            {chatData?.data?.map((message) => (
              <div
                key={message.id}
                className={`mb-6 flex flex-col ${
                  message.role === "user" ? "items-end" : "items-start"
                }`}
              >
                {/* Message Bubble */}
                <div
                  className={`rounded-2xl p-4 max-w-[80%] ${
                    message.role === "user"
                      ? "bg-white border border-gray-200 text-black"
                      : "bg-gray-100 text-black"
                  }`}
                  style={{ fontFamily: "Silka", whiteSpace: "pre-line" }}
                >
                  {message.content}
                </div>

                {/* Suggested Modules Section */}
                {message.role === "assistant" &&
                  message.suggestedModules &&
                  message.suggestedModules.length > 0 && (
                    <div className="mt-3 max-w-[80%] w-full">
                      {" "}
                      {/* Aligns with bubble width */}
                      <h3
                        className="text-md font-semibold mb-2 text-gray-700"
                        style={{ fontFamily: "Archia Semibold" }}
                      >
                        Suggested Modules:
                      </h3>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        {message.suggestedModules.map((module) => {
                          const descriptionText = module.notes
                            ? module.notes.replace(/<[^>]+>/g, "")
                            : "No description available.";

                          let levelBorderColor = "border-coalColor";
                          const experienceLower =
                            module.experience?.toLowerCase();
                          if (
                            ["beginner", "entry", "entry level"].includes(
                              experienceLower
                            )
                          ) {
                            levelBorderColor = "border-[#0B5B23]";
                          } else if (
                            ["intermediate", "mid level", "mid"].includes(
                              experienceLower
                            )
                          ) {
                            levelBorderColor = "border-[#FFB500]";
                          } else if (
                            ["advanced", "senior", "senior level"].includes(
                              experienceLower
                            )
                          ) {
                            levelBorderColor = "border-[#FF5812]";
                          }

                          return (
                            <div
                              key={module.id}
                              className="border border-gray-200 rounded-lg overflow-hidden bg-white shadow-sm flex flex-col"
                            >
                              <div className="p-5 flex-grow">
                                <div className="flex justify-between items-center mb-4">
                                  <h4
                                    className="text-lg font-semibold"
                                    style={{ fontFamily: "Archia Semibold" }}
                                  >
                                    {module.name}
                                  </h4>
                                  <span
                                    className={`text-xs px-3 py-1 rounded-full border ${levelBorderColor}`}
                                    style={{ fontFamily: "Silka" }}
                                  >
                                    {module.experience || "N/A"}
                                  </span>
                                </div>
                                <div className="flex items-center text-sm text-gray-500 mb-4">
                                  <BiTime className="mr-1" />
                                  <span style={{ fontFamily: "Silka" }}>
                                    {module.time
                                      ? `${module.time} mins`
                                      : "N/A"}
                                  </span>
                                </div>
                                <p
                                  className="text-sm text-gray-600 mb-4 h-16 overflow-hidden"
                                  style={{ fontFamily: "Silka" }}
                                  title={descriptionText}
                                >
                                  {descriptionText}
                                </p>
                              </div>
                              <div className="p-5 pt-0">
                                <button
                                  onClick={() => {
                                    setModuleID(module.id);
                                    setModuleDetailsModal(true);
                                  }}
                                  className="w-full bg-primaryGreen border border-coalColor text-coalColor font-medium py-3 rounded-md hover:bg-coalColor hover:text-primaryGreen transition-colors"
                                  style={{ fontFamily: "Silka" }}
                                >
                                  Details
                                </button>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  )}
              </div>
            ))}
            {/* Skeleton loader for assistant response */}
            {messageLoading && (
              <div className="mb-6 flex justify-start">
                <div className="rounded-2xl px-4 py-3 bg-gray-100">
                  {" "}
                  {/* Adjusted padding for dots */}
                  <div className="flex items-center space-x-1.5">
                    {" "}
                    {/* Container for typing dots */}
                    <div className="h-2 w-2 bg-gray-400 rounded-full animate-bounce-dot [animation-delay:-0.30s]"></div>
                    <div className="h-2 w-2 bg-gray-400 rounded-full animate-bounce-dot [animation-delay:-0.15s]"></div>
                    <div className="h-2 w-2 bg-gray-400 rounded-full animate-bounce-dot"></div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>
        </div>

        {/* Input area */}
        <div className="border-t border-gray-200 p-4 bg-white">
          <div className="max-w-4xl mx-auto">
            <form onSubmit={handleSendMessage} className="relative">
              <input
                type="text"
                className="w-full px-6 py-4 pr-16 border border-gray-300 rounded-full focus:outline-none focus:border-gray-400"
                placeholder="Need a test for a role? Type it here."
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                style={{ fontFamily: "Silka" }}
              />
              <button
                type="submit"
                className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-primaryGreen rounded-full p-3"
              >
                <GoArrowRight className="text-xl" />
              </button>
            </form>
          </div>
        </div>
      </div>
      {ModuleDetailsModal && (
        <div
          className="relative z-10"
          aria-labelledby="modal-title"
          role="dialog"
          aria-modal="true"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>
          <div
            className="fixed inset-0 z-10 overflow-y-auto"
            onClick={() => setModuleDetailsModal(false)}
          >
            <div className="flex items-start justify-end  text-center">
              <div
                className="relative transform overflow-x-scroll no-scrollbar bg-[#F8F8F8] text-left shadow-xl transition-all sm:w-full md:w-1/2"
                onClick={handleButtonClick}
              >
                <div className="bg-[#F8F8F8]  h-screen px-4 sm:px-6 no-scrollbar overflow-auto pb-8">
                  {isLoadingModule ? (
                    <div class="loader-container-1">
                      <div class="loader-1"></div>
                    </div>
                  ) : (
                    <>
                      <div
                        className="flex justify-end sticky top-0 "
                        style={{ paddingTop: "100px" }}
                      >
                        <img
                          src={closem}
                          className="w-8 h-8 cursor-pointer"
                          onClick={() => setModuleDetailsModal(false)}
                        />
                      </div>
                      <div className="sm:px-0 md:px-0 lg:px-0 xl:px-10">
                        <h1 className="mt-4 text-xl font-bold">
                          {ModuleData?.name}
                        </h1>
                        <div className="grid sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-4 bg-coalColor mt-10 rounded-lg">
                          <div className="p-2 pl-5 border-r py-5">
                            <img src={d1} className="w-8 h-8" />
                            <p className="text-lg text-white mt-1">
                              {ModuleData?.module_type === "multiple_choice"
                                ? "Multiple Choice"
                                : "Case Study"}
                            </p>
                            <p className="text-xs text-white">
                              {ModuleData?.category?.categoryName}
                            </p>
                          </div>
                          <div className="p-2 pl-5 border-r py-5">
                            <img src={d2} className="w-8 h-8" />
                            <p className="text-lg text-white mt-1">Time</p>
                            <p className="text-xs text-white">
                              {ModuleData?.time} mins
                            </p>
                          </div>
                          <div className="p-2 pl-5 border-r py-5">
                            <img src={d3} className="w-8 h-8" />
                            <p className="text-lg text-white mt-1">Language</p>
                            <p className="text-xs text-white">English</p>
                          </div>
                          <div className="p-2 pl-5 border-r py-5">
                            <img src={d4} className="w-8 h-8" />
                            <p className="text-lg text-white mt-1">Level</p>
                            <p className="text-xs text-white">
                              {ModuleData?.experience === "General"
                                ? "All levels"
                                : ModuleData?.experience}
                            </p>
                          </div>
                        </div>
                        <h1
                          className="mt-10 text-xl font-bold"
                          style={{ fontFamily: "Archia Bold" }}
                        >
                          Covered Skills
                        </h1>
                        <div
                          className={styles["html-content"]}
                          style={{ fontFamily: "Silka" }}
                        >
                          {ReactHtmlParser(ModuleData?.covering_skills)}
                        </div>
                        <h1
                          className="mt-10 text-xl font-bold"
                          style={{ fontFamily: "Archia Bold" }}
                        >
                          This module is relevant for
                        </h1>
                        <div
                          className="text-[#767676]"
                          style={{ fontFamily: "Silka" }}
                        >
                          <div className={styles["html-content"]}>
                            {ReactHtmlParser(ModuleData?.test_relevent_for)}
                          </div>
                        </div>
                        <h1
                          className="mt-10 text-xl font-bold"
                          style={{ fontFamily: "Archia Bold" }}
                        >
                          Description
                        </h1>
                        <div
                          className={styles["html-content"]}
                          style={{ fontFamily: "Silka" }}
                        >
                          {ReactHtmlParser(ModuleData?.notes)}
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default Chat;
